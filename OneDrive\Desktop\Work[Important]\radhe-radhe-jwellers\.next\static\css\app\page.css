/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/page.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
.page_page__ZU32B {
  --gray-rgb: 0, 0, 0;
  --gray-alpha-200: rgba(var(--gray-rgb), 0.08);
  --gray-alpha-100: rgba(var(--gray-rgb), 0.05);

  --button-primary-hover: #383838;
  --button-secondary-hover: #f2f2f2;

  display: grid;
  grid-template-rows: 20px 1fr 20px;
  align-items: center;
  justify-items: center;
  min-height: 100svh;
  padding: 80px;
  grid-gap: 64px;
  gap: 64px;
  font-family: var(--font-geist-sans);
}

@media (prefers-color-scheme: dark) {
  .page_page__ZU32B {
    --gray-rgb: 255, 255, 255;
    --gray-alpha-200: rgba(var(--gray-rgb), 0.145);
    --gray-alpha-100: rgba(var(--gray-rgb), 0.06);

    --button-primary-hover: #ccc;
    --button-secondary-hover: #1a1a1a;
  }
}

.page_main__GlU4n {
  display: flex;
  flex-direction: column;
  gap: 32px;
  grid-row-start: 2;
}

.page_main__GlU4n ol {
  font-family: var(--font-geist-mono);
  padding-left: 0;
  margin: 0;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.01em;
  list-style-position: inside;
}

.page_main__GlU4n li:not(:last-of-type) {
  margin-bottom: 8px;
}

.page_main__GlU4n code {
  font-family: inherit;
  background: var(--gray-alpha-100);
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

.page_ctas__g5wGe {
  display: flex;
  gap: 16px;
}

.page_ctas__g5wGe a {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 128px;
  height: 48px;
  padding: 0 20px;
  border: 1px solid transparent;
  transition:
    background 0.2s,
    color 0.2s,
    border-color 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
}

a.page_primary__V8M9Y {
  background: var(--foreground);
  color: var(--background);
  gap: 8px;
}

a.page_secondary__lm_PT {
  border-color: var(--gray-alpha-200);
  min-width: 158px;
}

.page_footer__sHKi3 {
  grid-row-start: 3;
  display: flex;
  gap: 24px;
}

.page_footer__sHKi3 a {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page_footer__sHKi3 img {
  flex-shrink: 0;
}

/* Enable hover only on non-touch devices */
@media (hover: hover) and (pointer: fine) {
  a.page_primary__V8M9Y:hover {
    background: var(--button-primary-hover);
    border-color: transparent;
  }

  a.page_secondary__lm_PT:hover {
    background: var(--button-secondary-hover);
    border-color: transparent;
  }

  .page_footer__sHKi3 a:hover {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
}

@media (max-width: 600px) {
  .page_page__ZU32B {
    padding: 32px;
    padding-bottom: 80px;
  }

  .page_main__GlU4n {
    align-items: center;
  }

  .page_main__GlU4n ol {
    text-align: center;
  }

  .page_ctas__g5wGe {
    flex-direction: column;
  }

  .page_ctas__g5wGe a {
    font-size: 14px;
    height: 40px;
    padding: 0 16px;
  }

  a.page_secondary__lm_PT {
    min-width: auto;
  }

  .page_footer__sHKi3 {
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
}

@media (prefers-color-scheme: dark) {
  .page_logo__7fc9l {
    filter: invert();
  }
}

